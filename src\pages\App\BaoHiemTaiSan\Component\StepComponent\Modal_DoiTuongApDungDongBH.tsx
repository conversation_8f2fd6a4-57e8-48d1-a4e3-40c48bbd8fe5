import {CheckOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, InputCellTable, Popcomfirm} from "@src/components";
import {defaultPaginationTableProps, fillRowTableEmpty} from "@src/hooks";
import {Checkbox, Col, Flex, Form, Modal, Row, Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultFormValueTimKiemDoiTuongBaoHiemTaiSan} from "../../index.configs";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {FormTimKiemDoiTuongBaoHiemTaiSan, tableDoiTuongApDungDongBHColumn, TableDoiTuongColumnDataType} from "./Constant";

const PAGE_SIZE = 5; // khai báo khác default cho vừa màn hình
const {nd_tim, dong_tai} = FormTimKiemDoiTuongBaoHiemTaiSan;
export interface IModalDoiTuongApDungRef {
  open: (donViDong?: CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaDuocApDungDongBH?: []) => void;
  close: () => void;
}

const ModalDoiTuongApDungDongComponent = forwardRef<IModalDoiTuongApDungRef, {disabled?: boolean}>(({disabled = false}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (donViDong?: CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaDuocApDungDongBH?: []) => {
      setIsOpen(true);
      if (donViDong) {
        setChiTietDonViDong(donViDong);
      }
      setSelectedItems(listDoiTuongDaDuocApDungDongBH || []);
      // Gọi API tìm kiếm với dữ liệu mặc định
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      if (so_id) {
        const defaultParams = {
          ...defaultFormValueTimKiemDoiTuongBaoHiemTaiSan,
          so_id,
          trang: 1,
          ma_dvi_dong_tai: donViDong?.ma_dvi_dong,
          dong_tai: "",
          so_dong: PAGE_SIZE,
        };
        timKiemPhanTrangDoiTuongBaoHiemTaiSan(defaultParams);
      }
    },
    close: () => setIsOpen(false),
  }));

  const {loading, tongSoDongDoiTuongBaoHiemTaiSan, danhSachDoiTuongBaoHiemTaiSan, timKiemPhanTrangDoiTuongBaoHiemTaiSan, chiTietHopDongBaoHiemTaiSan, updateDoiTuongApDungTyLeDongBH} =
    useBaoHiemTaiSanContext();

  const [formTimKiemPhanTrangDoiTuongBaoHiemXe] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [dataTableListDoiTuongBaoHiemXe, setDataTableListDoiTuongBaoHiemXe] = useState<Array<TableDoiTuongColumnDataType>>([]);
  const [checkboxTyleChecked, setCheckboxTyleChecked] = useState<boolean>(true);
  const [selectedItems, setSelectedItems] = useState<Array<TableDoiTuongColumnDataType>>([]);
  const [chiTietDonViDong, setChiTietDonViDong] = useState<CommonExecute.Execute.IDongBaoHiem | null>(null);

  //sử dụng ref để state mới cập nhật sử dụng được luôn (checkboxTyleChecked, selectedItems)
  const checkboxTyleCheckedRef = useRef(checkboxTyleChecked);
  const selectedItemsRef = useRef(selectedItems);

  useEffect(() => {
    checkboxTyleCheckedRef.current = checkboxTyleChecked;
  }, [checkboxTyleChecked]);

  useEffect(() => {
    selectedItemsRef.current = selectedItems;
  }, [selectedItems]);

  //Init data khi mở modal
  const initDataTable = useMemo(() => {
    try {
      //match mảng đối tượng đã được áp dụng với mảng data phân trang
      const selectedSoIdDtMap = new Map(selectedItems.map(item => [item.so_id_dt, item]));
      const tableData = danhSachDoiTuongBaoHiemTaiSan.map((item: any, index: number) => {
        const selectedItem = selectedSoIdDtMap.get(item.so_id_dt);
        return {
          ...item,
          bien_xe: `${item.bien_xe || "-"} / ${item.so_khung || "-"} / ${item.so_may || "-"}`,
          key: index.toString(),
          ap_dung: selectedItem ? "C" : "K",
          tl_dong: selectedItem ? selectedItem.tl_dong : item.tl_dong,
        };
      });
      const arrEmptyRow: Array<TableDoiTuongColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      setDataTableListDoiTuongBaoHiemXe([...tableData, ...arrEmptyRow]);
    } catch (error) {
      setDataTableListDoiTuongBaoHiemXe([]);
      setSelectedItems([]);
    }
  }, [danhSachDoiTuongBaoHiemTaiSan, selectedItems]);

  //xử lý validate form
  useEffect(() => {
    if (selectedItems.length <= 0) {
      setDisableSubmit(true);
      return;
    } else setDisableSubmit(false);
  }, [selectedItems]);

  // Hàm xử lý thay đổi giá trị input của bảng
  const handleTableInputChange = (index: number, dataIndex: string, value: any) => {
    if (dataIndex === "tl_dong") {
      const numericValue = value.replace(/[^0-9]/g, "");
      if (numericValue.length > 3) return;
      const numValue = parseInt(numericValue) || 0;
      const finalValue = numValue > 100 ? "100" : numericValue;

      setDataTableListDoiTuongBaoHiemXe(prev => {
        const newData = [...prev];
        // Sử dụng ref để lấy giá trị mới nhất
        if (checkboxTyleCheckedRef.current && selectedItemsRef.current.length > 0) {
          // Cập nhật tất cả các item được chọn
          selectedItemsRef.current.forEach(selectedItem => {
            const itemIndex = newData.findIndex(item => item.key === selectedItem.key);
            if (itemIndex !== -1) {
              newData[itemIndex] = {
                ...newData[itemIndex],
                [dataIndex]: finalValue,
              } as TableDoiTuongColumnDataType;
            }
          });

          // Cập nhật selectedItems state để đồng bộ
          const updatedSelectedItems = selectedItemsRef.current.map(item => ({
            ...item,
            [dataIndex]: finalValue,
          }));
          setSelectedItems(updatedSelectedItems);
        } else {
          // Cập nhật chỉ item tại index được truyền vào
          if (index >= 0 && index < newData.length) {
            newData[index] = {
              ...newData[index],
              [dataIndex]: finalValue,
            } as TableDoiTuongColumnDataType;

            // Cập nhật selectedItems nếu item này đang được chọn
            const currentItem = newData[index];
            // Chỉ cập nhật item trong selectedItems nếu nó đã được chọn (ap_dung === "C")
            if (currentItem.ap_dung === "C") {
              setSelectedItems(prevSelectedItems => prevSelectedItems.map(item => (item.key === currentItem.key ? {...item, [dataIndex]: finalValue} : item)));
            }
          }
        }
        return newData;
      });
    }
  };

  // Handle change checkbox Áp dụng
  const handleCheckboxChange = (key: string, checked: boolean) => {
    const item = dataTableListDoiTuongBaoHiemXe.find(i => i.key === key);
    if (!item) return;
    const updatedItem = {...item, ap_dung: checked ? "C" : "K"};

    setSelectedItems(prev => {
      const index = prev.findIndex(i => i.so_id_dt === updatedItem.so_id_dt);
      if (checked) {
        // Nếu check thì thêm/cập nhật vào mảng
        if (index !== -1) {
          // Cập nhật
          const newArr = [...prev];
          newArr[index] = {...newArr[index], ap_dung: "C"};
          return newArr;
        } else {
          // Thêm mới
          return [...prev, updatedItem];
        }
      } else {
        // Nếu uncheck thì loại bỏ khỏi mảng
        if (index !== -1) {
          const newArr = [...prev];
          newArr.splice(index, 1);
          return newArr;
        }
        return prev;
      }
    });
  };

  //Bấm Update
  const onPressLuuDoiTuongApDung = async () => {
    try {
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      const ma_dvi_dong = chiTietDonViDong?.ma_dvi_dong;
      const gcn =
        selectedItems.length > 0
          ? selectedItems.map(item => ({
              so_id_dt: Number(item.so_id_dt || 0),
              tl_dong: Number(item.tl_dong || 0),
            }))
          : [];

      if (!so_id || !ma_dvi_dong) {
        return;
      }
      const params = {so_id, ma_dvi_dong, gcn};
      const response = await updateDoiTuongApDungTyLeDongBH(params);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // Gộp hàm xử lý tìm kiếm và chuyển trang
  const handleSearchAndPagination = useCallback(
    (values?: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams & Partial<ReactQuery.IPhanTrang>, pageArg?: number) => {
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      if (values) {
        const cleanedValues = {
          ...values,
          nd_tim: values.nd_tim ?? "",
          dong_tai: values.dong_tai ?? "",
          so_id,
          ma_dvi_dong_tai: chiTietDonViDong?.ma_dvi_dong,
        };
        setPage(1);
        timKiemPhanTrangDoiTuongBaoHiemTaiSan({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
      } else {
        const page = pageArg || 1;
        setPage(page);
        const params = {
          ...defaultFormValueTimKiemDoiTuongBaoHiemTaiSan,
          so_id,
          trang: page,
          so_dong: PAGE_SIZE,
          ma_dvi_dong_tai: chiTietDonViDong?.ma_dvi_dong,
        };
        timKiemPhanTrangDoiTuongBaoHiemTaiSan(params);
      }
    },
    [chiTietHopDongBaoHiemTaiSan, timKiemPhanTrangDoiTuongBaoHiemTaiSan, chiTietDonViDong],
  );

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    // Reset form tìm kiếm
    formTimKiemPhanTrangDoiTuongBaoHiemXe.resetFields();
    // Reset các state
    setSelectedItems([]);
    setCheckboxTyleChecked(true);
    setPage(1);
    setChiTietDonViDong(null);
    setDataTableListDoiTuongBaoHiemXe([]);
  };

  // Handler change checkbox tỷ lệ
  const handleCheckboxTyleChange = (checked: boolean) => {
    setCheckboxTyleChecked(checked);
  };

  //RENDER
  const renderFormInputColum = (props?: any, span = 10) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  // Clone columns và thay title cột 'Tỷ lệ' thành Checkbox
  const columnsWithCheckbox = useMemo(() => {
    return (tableDoiTuongApDungDongBHColumn || []).map(col => {
      if ("dataIndex" in col && col.dataIndex === "tl_dong") {
        return {
          ...col,
          title: (
            <span className="flex w-full items-center justify-center gap-1">
              <Checkbox checked={checkboxTyleChecked} onChange={e => handleCheckboxTyleChange(e.target.checked)} />
              Tỷ lệ
            </span>
          ),
        };
      }
      return col;
    });
  }, [checkboxTyleChecked]);

  //render cột
  const renderColumn = (column: TableColumnType<TableDoiTuongColumnDataType>) => {
    if (!("dataIndex" in column)) return column;

    //nhập tỷ lệ
    if (["tl_dong"].includes(column.dataIndex as string)) {
      return {
        ...column,
        render: (value: any, record: TableDoiTuongColumnDataType, index: number) =>
          record.key && record.key.toString().startsWith("empty") ? null : (
            <InputCellTable
              component="input"
              key={`${record.key}-${column.dataIndex}`}
              value={value || ""}
              index={index}
              dataIndex={column.dataIndex as string}
              onChange={handleTableInputChange}
              disabled={record.ap_dung !== "C"}
              maxLength={3}
            />
          ),
      };
    }
    //check box
    if (column.dataIndex === "ap_dung") {
      return {
        ...column,
        render: (_: any, record: TableDoiTuongColumnDataType) =>
          record.key && record.key.toString().startsWith("empty") ? null : (
            <FormInput className="!mb-0" component="checkbox" checked={record.ap_dung === "C"} onChange={e => handleCheckboxChange(record.key, e.target.checked)} />
          ),
      };
    }

    return column;
  };

  //render header table đối tượng
  const renderHeaderTableDoiTuongBaoHiemXe = () => {
    return (
      <Form form={formTimKiemPhanTrangDoiTuongBaoHiemXe} layout="vertical" className="mt-5 [&_.ant-form-item]:mb-2" onFinish={handleSearchAndPagination}>
        <Row gutter={16}>
          {renderFormInputColum({...nd_tim})}
          {renderFormInputColum({...dong_tai}, 10)}
          <Col span={4}>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formNhapKyThanhToan" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressLuuDoiTuongApDung}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin đối tượng áp dụng không?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit || disabled}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="custom-full-modal"
        title={
          // "Kỳ thanh toán"
          <HeaderModal title={"Đối tượng áp dụng"} />
        }
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"45vw"}
        maskClosable={false}
        footer={renderFooter}>
        {renderHeaderTableDoiTuongBaoHiemXe()}
        <Table<TableDoiTuongColumnDataType>
          className="hide-scrollbar no-header-border-radius"
          columns={columnsWithCheckbox.map(renderColumn)}
          dataSource={dataTableListDoiTuongBaoHiemXe as TableDoiTuongColumnDataType[]}
          scroll={{y: 300}}
          bordered
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongDoiTuongBaoHiemTaiSan,
            pageSize: PAGE_SIZE,
            current: page, //set current page
            onChange: (page, pageSize) => {
              handleSearchAndPagination(undefined, page);
            },
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
        />
      </Modal>
    </Flex>
  );
});
ModalDoiTuongApDungDongComponent.displayName = "ModalDoiTuongApDungDongComponent";
export const ModalDoiTuongApDungDongBH = memo(ModalDoiTuongApDungDongComponent, isEqual);
